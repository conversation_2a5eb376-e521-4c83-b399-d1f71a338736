.judge-questions-container {
  // width: 1000px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  padding-top: 36px;
  // label名字title
  .judge-questions-title {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 140px;
    line-height: 40px;
    font-size: 16px;
    color: rgba(51, 51, 51, 1);
    &:before {
      display: inline-block;
      margin-right: 4px;
      margin-top: 1px;
      color: #f5222d;
      font-size: 16px;
      content: '*';
    }
  }
}
.judge-questions-btns-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 20px auto;
  width: 952px;
  .judge-questions-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 40px;
    font-size: 16px;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    border-radius: 10px;
  }
  .judge-questions-submit {
    margin-left: 40px;
    background-color: #4390f7;
    color: #fff;
    border: 1px solid #4390f7;
  }
  .judge-questions-disabled-submit {
    opacity: 0.5;
  }
}
