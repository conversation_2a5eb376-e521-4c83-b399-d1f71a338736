import { LoadingOutlined, PlusOutlined } from '@ant-design/icons'
import { saveUserInfo } from '@features/userInfoSlice'
import req from '@utils/request'
import { getUploadHeaders, getUserInfo as getStoredUserInfo, getLoginId } from '@utils/auth'
import { Button, Card, Col, Form, Input, Radio, Row, Upload, message, Spin } from 'antd'
import { memo, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'

import './index.less'

const { TextArea } = Input
const apiName = {
  update: '/user/update',
  queryInfo: '/user/getUserInfo'
}

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 }
}

interface UserInfo {
  nickName?: string
  phone?: string
  email?: string
  sex?: string | number
  introduce?: string
  avatar?: string
}

const Sex: Record<string, any> = {
  1: '男',
  2: '女'
}

const normFile = (e: any) => {
  if (Array.isArray(e)) {
    return e
  }
  return e?.fileList
}

const UserInfo = () => {
  const loginId = getLoginId()

  const dispatch = useDispatch()

  const [form] = Form.useForm()
  const [editFlag, setEditFlag] = useState(false)
  const [loading, setLoading] = useState(false)
  const [pageLoading, setPageLoading] = useState(true)
  const [userInfo, setUserInfo] = useState<UserInfo>({})
  const [avatar, setAvatar] = useState()

  const getUserInfo = async () => {
    setPageLoading(true)
    try {
      const res = await req(
        {
          method: 'post',
          url: apiName.queryInfo,
          data: {
            userName: loginId
          }
        },
        '/auth'
      )
      if (res?.success && res?.data) {
        setUserInfo(res.data)
        // 转换头像URL为代理URL
        const avatarUrl = res.data.avatar || ''
        const proxyAvatarUrl = avatarUrl ? convertMinioUrl(avatarUrl) : ''
        console.log('初始化头像 - 原始URL:', avatarUrl)
        console.log('初始化头像 - 代理URL:', proxyAvatarUrl)
        setAvatar(proxyAvatarUrl)
        form.setFieldsValue(res.data)
      } else {
        message.error('获取用户信息失败')
      }
    } catch (error) {
      message.error('网络错误，请稍后重试')
    } finally {
      setPageLoading(false)
    }
  }

  useEffect(() => {
    if (loginId) {
      getUserInfo()
    } else {
      // 如果没有登录信息，显示提示
      message.warning('请先登录')
    }
  }, [loginId])

  const onFinish = () => {
    setLoading(true)
    const values = form.getFieldsValue()
    if (!Object.values(values).filter(Boolean).length && !avatar) {
      setLoading(false)
      return
    }
    const params = {
      userName: loginId,
      ...values
    }
    if (avatar) {
      params.avatar = avatar
    }
    req(
      {
        method: 'post',
        url: apiName.update,
        data: { ...params }
      },
      '/auth'
    )
      .then(res => {
        dispatch(saveUserInfo(params))
        setUserInfo(params)
        // 确保更新后的头像也使用代理URL
        const avatarUrl = params.avatar || ''
        const proxyAvatarUrl = avatarUrl ? convertMinioUrl(avatarUrl) : ''
        setAvatar(proxyAvatarUrl)
        if (res.success) {
          message.success('更新成功')
          setTimeout(() => {
            // getUserInfo()
            setLoading(false)
            setEditFlag(false)
          }, 500)
        } else {
          setLoading(false)
        }
      })
      .catch(() => {
        message.error('更新失败')
        setLoading(false)
      })
  }

  // 转换MinIO URL为代理URL以解决跨域问题
  const convertMinioUrl = (url) => {
    if (url && url.includes('115.190.94.237:9000')) {
      return url.replace('http://115.190.94.237:9000', '/minio')
    }
    return url
  }

  const handleChange = ({ file }) => {
    console.log('Upload file status:', file.status)
    console.log('Upload response:', file.response)

    if (file.status === 'done' && file.response) {
      let avatarUrl = null

      // 处理标准JSON格式响应
      if (file.response.success && file.response.data) {
        avatarUrl = typeof file.response.data === 'string'
          ? file.response.data
          : file.response.data.url
      }
      // 处理直接返回URL字符串的情况
      else if (typeof file.response === 'string' && file.response.startsWith('http')) {
        avatarUrl = file.response
      }

      if (avatarUrl) {
        // 转换MinIO URL为代理URL
        const proxyUrl = convertMinioUrl(avatarUrl)
        console.log('原始URL:', avatarUrl)
        console.log('代理URL:', proxyUrl)
        setAvatar(proxyUrl)
        return
      }

      console.error('无法从响应中提取头像URL:', file.response)
    } else if (file.status === 'error') {
      console.error('上传失败:', file.error)
    }
  }

  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>点击上传</div>
    </div>
  )

  if (pageLoading) {
    return (
      <div className='user-info-box'>
        <Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <div>加载中...</div>
        </Spin>
      </div>
    )
  }

  return (
    <div className='user-info-box'>
      <Card title='基本信息'>
        <Form {...layout} colon={false} form={form}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              {editFlag ? (
                <Form.Item label='用户头像' valuePropName='fileList' getValueFromEvent={normFile}>
                  <Upload
                    name='uploadFile'
                    listType='picture-card'
                    className='avatar-uploader'
                    accept='image/*'
                    showUploadList={false}
                    withCredentials
                    action='/oss/upload'
                    headers={getUploadHeaders()}
                    data={{
                      bucket: 'user',
                      objectName: 'icon'
                    }}
                    // beforeUpload={beforeUpload}
                    onChange={handleChange}
                  >
                    {avatar ? (
                      <img src={avatar} style={{ height: '80px', width: '80px' }} />
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                </Form.Item>
              ) : (
                <Form.Item label='用户头像'>
                  {userInfo.avatar ? (
                    <img className='user-info_header' src={userInfo.avatar} />
                  ) : (
                    <div />
                  )}
                  {/* <img className='user-info_header' src={userInfo.avatar || Head} /> */}
                </Form.Item>
              )}
            </Col>
            <Col span={24}>
              {editFlag ? (
                <Form.Item label='用户昵称' name='nickName'>
                  <Input placeholder='请输入昵称' />
                </Form.Item>
              ) : (
                <Form.Item label='用户昵称'>
                  <span>{userInfo.nickName || '未设置'}</span>
                </Form.Item>
              )}
            </Col>
            <Col span={24}>
              {editFlag ? (
                <Form.Item label='性别' name='sex'>
                  <Radio.Group>
                    <Radio value={1}>男</Radio>
                    <Radio value={2}>女</Radio>
                  </Radio.Group>
                </Form.Item>
              ) : (
                <Form.Item label='性别'>
                  <span>{userInfo.sex ? Sex[userInfo.sex] : '未设置'}</span>
                </Form.Item>
              )}
            </Col>
            <Col span={24}>
              {editFlag ? (
                <Form.Item label='手机号码' name='phone'>
                  <Input placeholder='请输入手机号码' />
                </Form.Item>
              ) : (
                <Form.Item label='手机号码'>
                  <span>{userInfo.phone || '未设置'}</span>
                </Form.Item>
              )}
            </Col>
            <Col span={24}>
              {editFlag ? (
                <Form.Item label='邮箱' name='email'>
                  <Input placeholder='请输入邮箱' />
                </Form.Item>
              ) : (
                <Form.Item label='邮箱'>
                  <span>{userInfo.email || '未设置'}</span>
                </Form.Item>
              )}
            </Col>
            <Col span={24}>
              {editFlag ? (
                <Form.Item label='个人简介' name='introduce'>
                  <TextArea placeholder='请输入个人简介' maxLength={500} showCount />
                </Form.Item>
              ) : (
                <Form.Item label='个人简介'>
                  <span>{userInfo.introduce || '这个人很懒，什么也没有留下。。。。'}</span>
                </Form.Item>
              )}
            </Col>

            <Col span={24}>
              <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
                <div style={{ display: 'flex', gap: '12px', marginTop: '20px' }}>
                  {editFlag ? (
                    <>
                      <Button
                        type='primary'
                        onClick={onFinish}
                        loading={loading}
                      >
                        保存
                      </Button>
                      <Button onClick={() => setEditFlag(false)}>取消</Button>
                    </>
                  ) : (
                    <Button type='primary' onClick={() => setEditFlag(true)}>
                      编辑
                    </Button>
                  )}
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  )
}

export default memo(UserInfo)
