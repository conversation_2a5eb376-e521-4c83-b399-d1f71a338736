{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"], // '@' 别名映射到 'src' 目录
      "@assets/*": ["src/assets/*"],
      "@views/*": ["src/views/*"],
      "@utils/*": ["src/utils/*"],
      "@components/*": ["src/components/*"],
      "@imgs/*": ["src/imgs/*"],
      "@constants/*": ["src/constants/*"],
      "@store/*": ["src/store/*"],
      "@features/*": ["src/store/features/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
