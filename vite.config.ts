import react from '@vitejs/plugin-react'
import { defineConfig, loadEnv } from 'vite'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            common: ['axios', 'lodash', 'pubsub-js', 'wangeditor']
          }
        }
      }
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@assets': path.resolve(__dirname, 'src/assets'),
        '@views': path.resolve(__dirname, 'src/views'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@imgs': path.resolve(__dirname, 'src/imgs'),
        '@constants': path.resolve(__dirname, 'src/constants'),
        '@store': path.resolve(__dirname, 'src/store'),
        '@features': path.resolve(__dirname, 'src/store/features')
      }
    },
    plugins: [react()],
    server: {
      port: 5173,
      host: '0.0.0.0',
      proxy: {
        '/subject': {
          target: env.VITE_API_HOST,
          changeOrigin: true
        },
        '/auth': {
          target: env.VITE_API_HOST,
          changeOrigin: true
        },
        '/oss': {
          target: env.VITE_API_HOST, // 使用网关地址 http://localhost:8080
          changeOrigin: true,
          secure: false,
          timeout: 30000,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('OSS代理错误:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('OSS代理请求 -> 网关:', req.method, req.url);
            });
          }
        },
        '/practice': {
          target: env.VITE_API_HOST,
          changeOrigin: true
        },
        '/circle': {
          target: env.VITE_API_HOST,
          changeOrigin: true
        },
        '/interview': {
          target: env.VITE_API_HOST,
          changeOrigin: true
        },
        '/minio': {
          target: 'http://**************:9000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/minio/, ''),
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('MinIO代理错误:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('MinIO代理请求:', req.method, req.url);
            });
          }
        }
      }
    }
  }
})
