// 推荐
.assessment-report-recommend {
    margin-top: 30px;
    padding: 30px 40px;
    background-color: #fff;
    .assessment-report-recommend-tip {
        position: relative;
        margin-bottom: 30px;
        margin-left: 16px;
        font-size: 16px;
        color: #333;
        &:before {
            content: '';
            position: absolute;
            left: -16px;
            top: 8px;
            width: 8px;
            height: 8px;
            background: rgba(255, 158, 32, 1);
        }
    }
    // 推荐列表
    .assessment-report-recommend-list {
        display: flex;
        .recommend-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            margin-right: 10px;
            padding: 20px;
            width: 220px;
            height: 130px;
            border-radius: 10px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: all 0.5s;
            &:hover {
                box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
            }
            .recommend-item-name {
                display: flex;
                justify-content: flex-start;
                margin-bottom: 10px;
                width: 100%;
                color: #fff;
                font-size: 16px;
                overflow: hidden;
                text-overflow: ellipsis; //省略
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            .recommend-item-heat {
                display: flex;
                justify-content: flex-end;
                width: 100%;
                color: #fff;
                font-size: 14px;
            }
        }
    }
}
