export const mockRankingModuleList = [
  {
    id: 1,
    title: '综合练习榜',
    rankingList: [
      {
        id: 1,
        wechatName: 'jcdw',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王1',
        subjectCount: 160
      },
      {
        id: 2,
        wechatName: 'jcdw1',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王2',
        subjectCount: 140
      },
      {
        id: 3,
        wechatName: 'jcdw2',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王',
        subjectCount: 101
      },
      {
        id: 4,
        wechatName: 'jcdw3',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅小王',
        subjectCount: 100
      },
      {
        id: 5,
        wechatName: 'jcdw4',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王5',
        subjectCount: 99
      }
    ]
  },
  {
    id: 2,
    title: '贡献榜',
    rankingList: [
      {
        id: 1,
        wechatName: 'jcdw5',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅小王',
        subjectCount: 160
      },
      {
        id: 2,
        wechatName: 'jcdw6',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王2',
        subjectCount: 150
      },
      {
        id: 3,
        wechatName: 'jcdw7',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王',
        subjectCount: 101
      },
      {
        id: 4,
        wechatName: 'jcdw8',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王4',
        subjectCount: 100
      },
      {
        id: 5,
        wechatName: 'jcdw9',
        createUserAvatar:
          'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg1.doubanio.com%2Fview%2Fnote%2Flarge%2Fpublic%2Fp37015927.jpg&refer=http%3A%2F%2Fimg1.doubanio.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1699286185&t=00db8ff5a1e11783f6c8eba954a5891f',
        createUser: '鸡翅大王5',
        subjectCount: 99
      }
    ]
  }
]

export const mockDataList = [
  {
    questionTitle: '什么是防抖和节流？有什么区别？如何实现',
    tags: [
      { name: '排序', id: 590 },
      { name: '数学', id: 5050 },
      { name: '穷举', id: 596 },
      { name: '贪心', id: 592 },
      { name: '二分', id: 5058 }
    ],
    id: 0,
    grade: 1,
    questionType: 6,
    difficulty: 1
  },
  {
    questionTitle: '非空数组，某元素只出现1次，其余出现2次，找到出现1次的元素',
    tags: [
      { name: '数学', id: 5050 },
      { name: '穷举', id: 596 }
    ],
    id: 1,
    grade: 2,
    questionType: 6,
    difficulty: 1
  },
  {
    questionTitle: 'Proxy、Observable的区别',
    tags: [
      { name: '贪心', id: 592 },
      { name: '二分', id: 5058 }
    ],
    id: 2,
    grade: 3,
    questionType: 3
  },
  {
    questionTitle: '谈谈浏览器的回流与重绘，如何优化dom渲染呢',
    tags: [
      { name: '字符串', id: 579 },
      { name: '模拟', id: 595 }
    ],
    id: 3,
    grade: 1,
    questionType: 1
  },
  {
    questionTitle: 'API指标有哪些，为什么有白屏现象，考虑哪些方向优化',
    tags: [
      { name: '字符串', id: 579 },
      { name: '模拟', id: 595 }
    ],
    id: 4,
    grade: 2,
    questionType: 4
  },
  {
    questionTitle: '将两个升序链表合并为一个新的升序链表并返回',
    tags: [{ name: '数学', id: 5050 }],
    id: 5,
    grade: 1,
    questionType: 2
  },
  {
    questionTitle: '对MVP架构的理解',
    tags: [
      { name: '递归', id: 591 },
      { name: '动态规划', id: 593 }
    ],
    id: 6,
    grade: 3,
    questionType: 5
  },
  {
    questionTitle: 'SPA页面的前端路由实现方案',
    tags: [
      { name: '排序', id: 590 },
      { name: '数组', id: 578 }
    ],
    id: 7,
    grade: 2,
    questionType: 1
  },
  {
    questionTitle: '怎么看 nodejs 可支持高并发',
    tags: [
      { name: '数学', id: 5050 },
      { name: '模拟', id: 595 }
    ],
    id: 8,
    grade: 2,
    questionType: 2
  },
  {
    questionTitle: '二叉树遍历',
    tags: [
      { name: '树', id: 583 },
      { name: '搜索', id: 3381 }
    ],
    id: 9,
    grade: 3,
    questionType: 4
  },
  {
    questionTitle: '玛雅人的密码',
    tags: [
      { name: '图', id: 584 },
      { name: '搜索', id: 3381 }
    ],
    id: 10,
    grade: 1,
    questionType: 3
  },
  { questionTitle: '求最大最小数', tags: [], id: 11, grade: 1, questionType: 1 },
  {
    questionTitle: '最小邮票数',
    tags: [{ name: '动态规划', id: 593 }],
    id: 12,
    grade: 1,
    questionType: 1
  },
  { questionTitle: 'abc', tags: [{ name: '穷举', id: 596 }], id: 13, grade: 1, questionType: 5 },
  {
    questionTitle: '求root(N, k)',
    tags: [
      { name: '递归', id: 591 },
      { name: '数学', id: 5050 },
      { name: '二分', id: 5058 }
    ],
    id: 14,
    grade: 1,
    questionType: 3
  },
  {
    questionTitle: 'n的阶乘',
    tags: [{ name: '数学', id: 5050 }],
    id: 15,
    grade: 1,
    questionType: 3
  },
  {
    questionTitle: '特殊乘法',
    tags: [
      { name: '模拟', id: 595 },
      { name: '数组', id: 578 },
      { name: '数学', id: 5050 }
    ],
    id: 16,
    grade: 1,
    questionType: 3
  },
  {
    questionTitle: '今年的第几天？',
    tags: [
      { name: '递归', id: 591 },
      { name: '数学', id: 5050 },
      { name: '穷举', id: 596 }
    ],
    id: 17,
    grade: 1,
    questionType: 2
  },
  {
    questionTitle: '完数VS盈数',
    tags: [{ name: '数学', id: 5050 }],
    id: 18,
    grade: 1,
    questionType: 3
  },
  {
    questionTitle: '递推数列',
    tags: [
      { name: '动态规划', id: 593 },
      { name: '数学', id: 5050 }
    ],
    id: 19,
    grade: 1,
    questionType: 1
  },
  { questionTitle: '最大序列和', tags: [{ name: '动态规划', id: 593 }], id: 20, grade: 1 },
  {
    questionTitle: '最小花费',
    tags: [
      { name: '动态规划', id: 593 },
      { name: '图', id: 584 }
    ],
    id: 21,
    grade: 1,
    questionType: 2
  },
  { questionTitle: 'N的阶乘', tags: [{ name: '数学', id: 5050 }], id: 22, grade: 1 },
  {
    questionTitle: '剩下的树',
    tags: [
      { name: '数组', id: 578 },
      { name: '数学', id: 5050 },
      { name: '哈希', id: 585 },
      { name: '栈', id: 581 }
    ],
    id: 23,
    grade: 1,
    questionType: 5
  },
  {
    questionTitle: '10进制 VS 2进制',
    tags: [
      { name: '数学', id: 5050 },
      { name: '位运算', id: 5074 }
    ],
    id: 24,
    grade: 1,
    questionType: 3
  },
  {
    questionTitle: '查找学生信息',
    tags: [
      { name: '数组', id: 578 },
      { name: '模拟', id: 595 }
    ],
    id: 25,
    grade: 1,
    questionType: 2
  }
]
/**
 * 一级分类
 */
export const mockTabList = [
  { id: 1, levelName: '全部', count: 100 },
  { id: 2, levelName: '前端', count: 1001 },
  { id: 3, levelName: '后端', count: 1005 },
  { id: 4, levelName: '测试', count: 1100 },
  { id: 5, levelName: '人工智能', count: 1200 },
  { id: 6, levelName: '产品', count: 1 },
  { id: 7, levelName: '视觉', count: 100 },
  { id: 8, levelName: '产品', count: 1 },
  { id: 9, levelName: '视觉', count: 100 },
  { id: 10, levelName: '产品', count: 1 },
  { id: 11, levelName: '视觉', count: 100 }
]

// 二级数据
export const mockCategoryList = {
  // 全部
  1: [
    {
      id: 1,
      levelName: '算法',
      childrenLevelList: [
        { id: 1, levelName: '双指针', count: 107 },
        { id: 2, levelName: '同向双指针', count: 57 },
        { id: 3, levelName: '相向双指针', count: 31 },
        { id: 4, levelName: '二分法', count: 95 },
        { id: 5, levelName: '二分答案', count: 28 },
        { id: 6, levelName: '分治法', count: 77 },
        { id: 7, levelName: '宽度优先搜索', count: 135 },
        { id: 8, levelName: '深度优先搜索/回溯法', count: 10 },
        { id: 9, levelName: '背包型动态规划', count: 224 },
        { id: 10, levelName: '状态压缩动态规划', count: 240 },
        { id: 11, levelName: '拓扑排序', count: 28 },
        { id: 12, levelName: '坐标型动态规划', count: 40 },
        { id: 13, levelName: '划分型动态规划', count: 15 },
        { id: 14, levelName: '记忆化搜索', count: 23 },
        { id: 15, levelName: '区间型动态规划', count: 22 },
        { id: 16, levelName: '动态规划', count: 3 },
        { id: 17, levelName: '博弈型动态规划', count: 4 },
        { id: 18, levelName: '匹配型动态规划', count: 15 },
        { id: 19, levelName: '树型动态规划', count: 4 },
        { id: 20, levelName: '排序算法', count: 83 },
        { id: 21, levelName: '外排序算法', count: 2 },
        { id: 22, levelName: '快速选择算法', count: 12 },
        { id: 23, levelName: '欧拉路径', count: 1 },
        { id: 24, levelName: '模拟法', count: 282 },
        { id: 25, levelName: '扫描线算法', count: 19 },
        { id: 26, levelName: '枚举法', count: 109 },
        { id: 27, levelName: '最短路', count: 6 },
        { id: 28, levelName: '贪心法', count: 85 },
        { id: 29, levelName: '最小生成树', count: 3 }
      ]
    },
    {
      id: 2,
      levelName: '数据结构',
      childrenLevelList: [
        { id: 1, levelName: '数组', count: 179 },
        { id: 2, levelName: '前缀和数组', count: 41 },
        { id: 3, levelName: '字符串', count: 257 },
        { id: 4, levelName: '链表', count: 52 },
        { id: 5, levelName: '双向链表', count: 1 },
        { id: 6, levelName: '队列', count: 18 },
        { id: 7, levelName: '单调队列', count: 3 },
        { id: 8, levelName: '双向队列', count: 3 },
        { id: 9, levelName: '栈', count: 74 },
        { id: 10, levelName: '单调栈', count: 27 },
        { id: 11, levelName: '二叉树', count: 120 },
        { id: 12, levelName: '树', count: 5 },
        { id: 13, levelName: '二叉搜索树', count: 30 },
        { id: 14, levelName: '迭代器', count: 6 },
        { id: 15, levelName: '堆', count: 45 },
        { id: 16, levelName: '图', count: 17 },
        { id: 17, levelName: '二分图', count: 5 },
        { id: 18, levelName: '哈希表', count: 156 },
        { id: 19, levelName: '字典树', count: 19 },
        { id: 20, levelName: '并查集', count: 36 },
        { id: 21, levelName: '树状数组', count: 7 },
        { id: 22, levelName: '线段树', count: 21 },
        { id: 23, levelName: '平衡树', count: 7 }
      ]
    },
    {
      id: 3,
      levelName: 'SQL 基础语法',
      childrenLevelList: [
        { id: 1, levelName: '比较运算符', count: 52 },
        { id: 2, levelName: '嵌套查询', count: 35 },
        { id: 3, levelName: '基础语法', count: 27 },
        { id: 4, levelName: '逻辑运算符', count: 25 },
        { id: 5, levelName: 'GROUP BY', count: 18 },
        { id: 6, levelName: '算术运算符', count: 17 },
        { id: 7, levelName: 'IN', count: 17 },
        { id: 8, levelName: 'ORDER BY', count: 16 },
        { id: 9, levelName: 'AS', count: 15 },
        { id: 10, levelName: 'HAVING', count: 8 },
        { id: 11, levelName: 'SELECT', count: 7 },
        { id: 12, levelName: 'DISTINCT', count: 6 },
        { id: 13, levelName: 'LIKE', count: 5 },
        { id: 14, levelName: 'LIMIT', count: 4 },
        { id: 15, levelName: 'IS NULL', count: 4 },
        { id: 16, levelName: 'UNION', count: 3 },
        { id: 17, levelName: '约束', count: 3 },
        { id: 18, levelName: 'IFNULL/COLLAPSE', count: 2 },
        { id: 19, levelName: 'ANY', count: 2 },
        { id: 20, levelName: 'ALL', count: 2 }
      ]
    }
  ],
  // 前端
  2: [
    {
      id: 1,
      levelName: '移动端',
      childrenLevelList: [
        {
          id: 1,
          levelName: 'IOSIOSIOSIOS',
          count: 13
        },
        {
          id: 2,
          levelName: '安卓',
          count: 130
        },
        {
          id: 3,
          levelName: '鸿蒙',
          count: 134
        }
      ]
    },
    {
      id: 2,
      levelName: '框架',
      childrenLevelList: [
        {
          id: 1,
          levelName: 'Vue.js',
          count: 13
        },
        {
          id: 2,
          levelName: 'React.js',
          count: 13
        },
        {
          id: 3,
          levelName: 'Bootstrap',
          count: 13
        }
      ]
    },
    {
      id: 3,
      levelName: '小程序',
      childrenLevelList: [
        { id: 1, levelName: '微信小程序', count: 13 },
        { id: 2, levelName: '京东小程序', count: 13 },
        { id: 3, levelName: '支付宝小程序', count: 13 },
        { id: 4, levelName: '百度小程序', count: 13 },
        { id: 5, levelName: 'QQ小程序', count: 13 }
      ]
    }
  ],
  // 后端
  3: [
    {
      id: 1,
      levelName: '框架',
      childrenLevelList: [
        {
          id: 1,
          levelName: 'Spring框架',
          count: 17
        },
        {
          id: 2,
          levelName: 'Struts框架',
          count: 127
        },
        {
          id: 3,
          levelName: 'Hibernate框架',
          count: 170
        }
      ]
    },
    {
      id: 2,
      levelName: 'SSM框架组合',
      childrenLevelList: [
        {
          id: 1,
          levelName: 'SpringMVC框架',
          count: 170
        },
        {
          id: 2,
          levelName: 'Mybatis框架',
          count: 1
        }
      ]
    }
  ],
  // 测试
  4: [
    {
      id: 1,
      levelName: '自动化测试',
      childrenLevelList: [
        {
          id: 1,
          levelName: '管理工具',
          count: 16
        },
        {
          id: 2,
          levelName: 'UI自动化',
          count: 16
        },
        {
          id: 3,
          levelName: '接口自动化',
          count: 16
        }
      ]
    }
  ],
  // 人工智能
  5: [
    {
      id: 1,
      levelName: '数据处理',
      childrenLevelList: [
        { id: 1, levelName: '工具库', count: 107 },
        { id: 2, levelName: '数据预处理', count: 127 },
        { id: 3, levelName: 'Numpy', count: 170 },
        { id: 4, levelName: 'Matplotlib', count: 170 },
        { id: 5, levelName: 'Pandas', count: 170 }
      ]
    },
    {
      id: 2,
      levelName: '机器学习',
      childrenLevelList: [
        { id: 1, levelName: '工具库', count: 170 },
        { id: 2, levelName: '分类', count: 1 },
        { id: 3, levelName: '模型', count: 170 },
        { id: 4, levelName: '决策树', count: 170 }
      ]
    }
  ],
  // 产品
  6: [],
  // 视觉
  7: []
}
