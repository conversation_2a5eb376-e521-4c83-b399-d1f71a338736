.wrapper{
  // padding: 0 24px;
  .listItem{
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    background-color: white;
    border-radius: 12px;
    padding: 18px;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 基础阴影 */
    transition: box-shadow 0.3s; /* 平滑过渡阴影变化 */
    &:hover{
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }
    .leftBox{
      margin-right: 20px;
      .scoreBox{
        width: 100px;
        height: 100px;
        font-size: 36px;
        text-align: center;
        line-height: 100px;
        border: 2px solid lightgray;
        border-radius: 10px;
      }
      .scoreLabel{
        text-align: center;
        margin-top: 10px;
      }
    }
    .rightBox{
      width: 100%;
      .rightItem{
        margin-bottom: 10px;
        &.title{
          font-weight: bold;
          font-size: 18px;
        }
        &.desc{
          line-height: 30px;
        }
        &:last-of-type{
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }
}
