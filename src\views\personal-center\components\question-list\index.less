.collection-bag-component-tab1-body {
  padding: 0 20px 20px 20px;
  .collection-bag-component-tab1-head-title {
    display: flex;
    color: #333;
    font-size: 16px;
    font-weight: 400;
    .collection-bag-component-tab1-head-title-icon {
      margin-right: 8px;
    }
  }
  .collection-bag-component-tab1-body-item {
    margin: 10px 0;
    padding: 10px 0;
    overflow: auto;
    position: relative;
    // clear: both;
    // height: 60px;
    border-bottom: 1px solid #e5e5e5;

    .collection-bag-component-tab1-body-item-question {
      font-size: 14px;
      line-height: 1.8;
      .collection-bag-component-tab1-body-item-question-content {
        width: 600px;
        cursor: pointer;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        &:hover {
          color: #3c6eee;
        }
      }
    }
    .collection-bag-component-tab1-body-item-foot {
      margin-top: 10px;
      float: right;
      .collection-bag-component-tab1-body-item-foot-button {
        cursor: pointer;
        color: #3c6eee;
      }
    }
  }
}
