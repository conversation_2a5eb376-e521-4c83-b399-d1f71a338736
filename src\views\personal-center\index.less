.personal-center-box {
  margin: 0 auto;
  width: 1200px;
  border-radius: 5px;
  overflow: auto;
  .personal-center-introduction {
    background-color: #fff;
    border-radius: 3px;
    height: 100px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .personal-center-introduction-detail {
      margin-left: 50px;
      // padding-top: 20px;
      display: flex;
      align-items: center;
      .personal-center-introduction-detail-headImg {
        margin-right: 20px;
        align-items: center;
      }
      .personal-center-introduction-detail-text {
        .personal-center-introduction-detail-name {
          color: #3c6eee;
          font-weight: 570;
        }
        .personal-center-introduction-detail-information {
          margin: 15px 0;
          .personal-center-introduction-detail-information-content {
            margin-right: 15px;
          }
        }
      }
    }
    .personal-center-introduction-result {
      margin-right: 50px;
      display: flex;
      align-items: center;
      .personal-center-introduction-result-item {
        text-align: center;
        padding-right: 10px;
        .personal-center-introduction-result-item-number {
          font-weight: 570;
          color: #3c6eee;
        }
      }
    }
  }
  .personal-center-content {
    display: flex;
    align-items: flex-start;
    .personal-center-content-left {
      background-color: #fff;
      overflow-y: auto;
      border-radius: 3px;
      width: 300px;
      min-height: 320px;
    }
    .personal-center-content-right {
      background-color: #fff;
      flex-direction: column;
      overflow: auto;
      border-radius: 4px;
      margin: 0 0 0 10px;
      width: 100%;
    }
  }
}
