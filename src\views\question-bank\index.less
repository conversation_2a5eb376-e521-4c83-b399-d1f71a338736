.question-bank-box {
  display: flex;
  width: 1200px;
  margin: 0 auto;
  background-color: #f3f4f6;
  border-radius: 5px;
  height: calc(100vh - 90px);
  overflow-y: scroll;
  .mask-box {
    flex: 1;
    overflow-y: auto;
    border-radius: 8px;
    .question-box {
      .category-list-box {
        padding: 24px 24px 6px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        background-color: #fff;
      }
      .question-list-box {
        margin-top: 1px;
      }
      .loading-more {
        background-color: white;
        text-align: center;
        padding-bottom: 15px;
      }
    }
  }

  .ranking-box {
    margin-left: 16px;
    overflow-y: auto;
    width: 310px;
  }
  .ant-spin-nested-loading {
    background-color: #fff;
  }
}
