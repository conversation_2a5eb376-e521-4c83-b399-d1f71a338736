.header-navigator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  min-width: 1200px;
  background-color: #fff;
  border-radius: 4px;
}

.nav-title {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  width: 1407px;
  margin: 0 auto;
  line-height: 50px;
  color: #1890ff;
  font-size: 24px;
  // font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", 微软雅黑,
  //     Arial, sans-serif;
}

.header-navigator .user {
  width: 36px;
  height: 36px;
  float: right;
  /* background: #1890ff; */
  border-radius: 50%;
  color: #fff;
  margin-top: 7px;
  line-height: 36px;
  text-align: center;
  font-size: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}

.jump-box {
  font-size: 14px;
  margin-right: 20px;
}

.drop-down-box {
  display: flex;
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  .drop-down-item {
    background-color: #eee;
    padding: 20px 30px;
    text-align: center;
    width: 200px;
    border-radius: 8px;
    cursor: pointer;
    &:hover {
      background-color: rgba(60, 110, 238, 0.9);
      color: white;
    }
    &-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    &-content {
      font-size: 12px;
    }
  }
}

.head-navigator-box {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #fff;
  border-radius: 4px;
}
.head-navigator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  width: 1435px;
}
.head-navigator-left {
  display: flex;
  align-items: center;
}
.head-navigator-logo {
  margin-right: 20px;
  // line-height: 50px;
  cursor: pointer;
  color: #1890ff;
  font-size: 24px;
  // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, 微软雅黑,
  //     Arial, sans-serif;
}
.head-navigator-select-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 500px;
}

.head-navigator-menu-box {
  display: flex;
}
.head-navigator-menu-box .ant-menu-horizontal {
  border-bottom: 0;
}
.ant-menu-horizontal > .ant-menu-item,
.ant-menu-horizontal > .ant-menu-submenu {
  padding: 0px;
  margin: 0 12px;
}
.head-navigator-input-box {
  margin-right: 24px;
}
.head-navigator-input-box .ant-input {
  border-radius: 16px;
}

.head-navigator-user-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
.head-navigator-bell {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
}
.head-navigator-bell:hover {
  background-color: rgba(0, 10, 32, 0.03);
}
.head-navigator-user-img {
  width: 36px;
  height: 36px;
  color: #fff;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}
