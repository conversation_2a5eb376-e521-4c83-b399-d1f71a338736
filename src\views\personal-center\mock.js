export const getCollectionList = {
  pageInfo: {
    pageSize: 10,
    totalPage: 2,
    pageIndex: 1,
    total: 12,
    firstPage: true,
    lastPage: false,
    startRecord: 0,
    endRecord: 10,
  },
  pageList: [
    {
      id: 1717,
      subjectName: '说说 React组件开发中关于作用域的常见问题。',
    },
    {
      id: 1710,
      subjectName: '概述一下 React中的事件处理逻辑。',
    },
    {
      id: 1706,
      subjectName: 'React中Diff算法的原理是什么？',
    },
    {
      id: 1698,
      subjectName: 'shouldComponentUpdate有什么用？为什么它很重要？',
    },
    {
      id: 1695,
      subjectName: '在哪个生命周期中你会发出Ajax请求？为什么？',
    },
    {
      id: 1692,
      subjectName: '类组件和函数组件之间的区别是什么？',
    },
    {
      id: 1691,
      subjectName: '详细解释 React 组件的生命周期方法？',
    },
    {
      id: 1689,
      subjectName: '什么是 Props?',
    },
    {
      id: 1579,
      subjectName: '横竖屏切换的Activity 生命周期变化？',
    },
    {
      id: 1578,
      subjectName: '谈下MVC ，MVP，MVVM',
    },
    {
      id: 1532,
      subjectName: 'get 和post 请求在缓存方面的区别是什么？',
    },
  ],
}
