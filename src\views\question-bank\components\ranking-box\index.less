// @import '@assets/style/mixin.scss';
.ranking-list-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 0 20px 16px;
  padding: 0px 16px 0px;
  // width: 310px;
  max-height: 416px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  .ranking-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 50px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid #f3f3f6;
    .ranking-list-title {
    }

    .ranking-list-btns {
      display: flex;
      .ranking-list-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        // width: 48px;
        padding: 0 10px;
        height: 30px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.5s;
        &:last-child {
          margin-right: 0px;
        }
      }
      .ranking-list-btn-active {
        font-weight: 600;
        border-bottom: 1px solid rgba(60, 110, 238, 1);
      }
    }
  }
  .ranking-list {
    font-size: 14px;
    .ranking-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px 10px 6px;
      color: #999999;
      font-size: 14px;
      cursor: pointer;
      .ranking-left {
        display: flex;
        align-items: center;
        .ranking-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;
          width: 24px;
          height: 24px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }
        .ranking-head-img {
          margin-right: 4px;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
          .ranking-head-icon {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }

        .ranking-info {
          .ranking-name {
            margin-bottom: 2px;
            color: #666666;
          }
          .ranking-department {
            font-size: 12px;
          }
        }
      }
      .ranking-right {
        display: flex;
        align-items: center;
        color: #c3c3c6;
      }
      &:hover {
        background-color: rgba(60, 110, 238, 0.05);
      }
    }
  }
  .ranking-btn-go {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-top: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    width: 230px;
    height: 36px;
    border-radius: 30px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
    background-color: rgba(60, 110, 238, 0.9);
    .ranking-btn-go-icon {
      width: 22px;
      height: 22px;
      background-size: 100% 100%;
      margin-right: 4px;
    }
    .ranking-btn-text {
      font-size: 14px;
      font-weight: bold;
      color: #fff;
    }
    &:hover {
      font-weight: bold;
    }
  }
  &:last-child {
    margin-bottom: 0;
  }
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
.tooltip-info {
  font-size: 12px;
}
.popover-img {
  margin-left: 4px;
  cursor: pointer;
  width: 16px;
  height: 16px;
}
