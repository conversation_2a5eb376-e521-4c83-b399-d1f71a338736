@import 'normalize.less';

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body,
html {
  font-family: -apple-system, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    '微软雅黑', <PERSON><PERSON>, sans-serif;
  width: 100%;
  height: 100%;
  color: #666666;
  background: #ffffff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a {
  text-decoration: none;
  color: #666666;
}

.clear-fix:after {
  font-size: 0;
  display: block;
  clear: both;
  height: 0;
  content: '';
}

.arrow-right {
  display: inline-block;
  width: 9px;
  height: 9px;
  margin-left: 0.1rem;
  transform: rotate(135deg);
  border-top: 1px solid #999999;
  border-left: 1px solid #999999;
  background-color: transparent;
}

//修改input高亮的问题
.ant-input-affix-wrapper {
  &:hover {
    border-color: none !important;
  }
}

.ant-input:focus {
  box-shadow: none;
}

.ant-input:focus,
.ant-input:hover {
  border-color: #d9d9d9;
}

.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border: 1px solid #d9d9d9;
}
