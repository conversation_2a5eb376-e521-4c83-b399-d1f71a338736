.text-area {
    background-color: #fff;
    font-size: 14px;
    .editorelem-menu {
        border: 1px solid #d9d9d9;
        .w-e-menu-tooltip {
            padding: 0px 6px;
            line-height: 28px;
        }
        .w-e-toolbar {
            z-index: 2 !important;
            border-radius: 12px;
        }
    }
    .editorelem-body {
        width: 100%;
        height: 100%;
        padding: 0 10px 10px;
        overflow-y: scroll;
        border: 1px solid #d9d9d9;
        border-top: none;
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px;
    }
}
.w-e-text-container {
    // z-index: 1 !important;
}
.kind-editor-active-box {
    border: 1px solid #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
