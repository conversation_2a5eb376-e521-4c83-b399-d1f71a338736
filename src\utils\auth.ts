/**
 * 认证相关工具函数
 */

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  const userInfoStorage = localStorage.getItem('userInfo')
  return userInfoStorage ? JSON.parse(userInfoStorage) : {}
}

/**
 * 获取token值
 */
export const getTokenValue = () => {
  const userInfo = getUserInfo()
  return userInfo.tokenValue || ''
}

/**
 * 获取token名称
 */
export const getTokenName = () => {
  const userInfo = getUserInfo()
  return userInfo.tokenName || 'satoken'
}

/**
 * 获取完整的token（包含前缀）
 */
export const getFullToken = () => {
  const tokenValue = getTokenValue()
  return tokenValue ? `yt ${tokenValue}` : ''
}

/**
 * 获取上传请求的headers
 */
export const getUploadHeaders = () => {
  const userInfo = getUserInfo()
  const tokenName = getTokenName() // 通常是 'token'
  const fullToken = getFullToken()
  return fullToken ? { [tokenName]: fullToken } : {}
}

/**
 * 获取登录用户ID
 */
export const getLoginId = () => {
  const userInfo = getUserInfo()
  return userInfo.loginId || ''
}

/**
 * 检查是否已登录
 */
export const isLoggedIn = () => {
  const userInfo = getUserInfo()
  return !!(userInfo.loginId && userInfo.tokenValue)
}
