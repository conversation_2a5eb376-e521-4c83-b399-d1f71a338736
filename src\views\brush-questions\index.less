.brush-questions-box {
  width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  padding: 20px 50px;
  border-radius: 5px;
  flex-direction: column;
  overflow: auto;
  .brush-questions-box,
  .question-box {
    flex-direction: column;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 8px;
    .question,
    .question-content {
      display: flex;
      .question-type,
      .question-number {
        display: flex;
        flex: 1;
        align-items: center;
      }
    }
  }
}
.question-box {
  margin-bottom: 10px;
}
.question {
  margin-bottom: 20px;
}
.question-type {
  color: rgba(60, 110, 238, 1) !important;
  font-size: 16px;
}
.question-number {
  justify-content: right;
  color: rgba(60, 110, 238, 1) !important;
  font-size: 16px;
  font-weight: 600;
}
.question-content {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 35px;
  margin-top: 50px;
  margin-bottom: 10px;
  .difficulty {
    margin-right: 10px;
    padding: 0 10px;
    border-radius: 4px;
    background-color: rgba(60, 110, 238, 0.3) !important;
  }
}
.reference-answer {
  color: rgba(60, 110, 238, 0.8) !important;
  font-size: 14px;
  padding: 20px 0 10px 0px;
}
.answer-content {
  background-color: rgba(60, 110, 238, 0.2) !important;
  padding: 15px 0 15px 35px;
  font-size: 14px;
  line-height: 35px;
  border-radius: 6px;
  margin-bottom: 5px;
  max-height: 500px;
  overflow-y: scroll;
}
.answer-box {
  border-radius: 0px;
  border-top: 1px dashed #e4e4e4;
  border-bottom: 1px dashed #e4e4e4;
}
.change-question-box {
  display: flex;
}

.right {
  display: flex;
  flex: 1;
  margin: 10px 0 10px 700px;
}

.jump-question {
  margin: 0 auto;
  text-align: center;
}
.last,
.next {
  // background-color: rgba(60, 110, 238,1) !important;
  margin: 0px 15px 15px 20px;
  font-size: 16px;
  cursor: pointer;
  width: 110px;
  height: 46px;
}
.last:active,
.next:active {
  background-color: rgba(60, 110, 238, 1) !important;
}
.number {
  width: 35px;
  height: 35px;
  line-height: 35px;
  border-radius: 50%;
  background-color: rgba(60, 110, 238, 1) !important;
  color: white;
  text-align: center;
}
.all-number {
  color: #34495e;
}
.type {
  padding: 2px 10px;
}
