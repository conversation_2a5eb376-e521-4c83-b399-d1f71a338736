@import '@assets/base.less';

.app-main {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  margin: 0 auto;
  position: absolute;
  min-width: 1200px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f3f4f6;
  padding: 66px 16px 0;
  overflow: hidden;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Chrome/Safari/Opera */
  -khtml-user-select: none; /* Konqueror */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently not supported by any browser */
  scrollbar-width: none;
  .content-box {
    width: 1200px;
    margin: 0 auto;
    overflow: auto;
    flex-grow: 1;
  }
  .copyright {
    height: 40px;
    line-height: 40px;
    text-align: center;
  }
}
